<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        @include('partials.head')
        <title>{{ $title ?? config('app.name') }}</title>
    </head>
    <body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 antialiased">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-white/20 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.15)_1px,transparent_0)] [background-size:20px_20px]"></div>

        <div class="relative flex min-h-screen flex-col items-center justify-center gap-8 p-6 md:p-10">
            <div class="flex w-full max-w-md flex-col gap-8">
                <!-- Logo Section -->
                <div class="text-center">
                    <a href="{{ route('home') }}" class="inline-flex flex-col items-center gap-3 font-medium group" wire:navigate>
                        <!-- Logo Icon -->
                        <div class="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-600 to-blue-700 shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
                            <i class="fas fa-home text-2xl text-white"></i>
                        </div>

                        <!-- Brand Name -->
                        <div class="flex flex-col items-center">
                            <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                                Lokus
                            </h1>
                            <p class="text-sm text-gray-600 mt-1">Find Your Perfect Home</p>
                        </div>
                    </a>
                </div>

                <!-- Auth Card -->
                <div class="flex flex-col gap-6">
                    <div class="rounded-2xl border border-white/20 bg-white/80 backdrop-blur-sm text-gray-800 shadow-xl">
                        <div class="px-8 py-10 md:px-10 md:py-12">
                            {{ $slot }}
                        </div>
                    </div>

                    <!-- Footer Links -->
                    <div class="text-center">
                        <div class="flex items-center justify-center space-x-6 text-sm text-gray-600">
                            <a href="{{ route('home') }}" class="hover:text-blue-600 transition-colors" wire:navigate>
                                <i class="fas fa-home mr-1"></i>
                                Home
                            </a>
                            <a href="mailto:<EMAIL>" class="hover:text-blue-600 transition-colors">
                                <i class="fas fa-envelope mr-1"></i>
                                Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @fluxScripts
    </body>
</html>
