<?php

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth')] class extends Component {
    public string $name = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';
    public string $role = 'seeker'; // Added for Lokus MVP

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $validated = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:' . User::class],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'string', 'in:seeker,lister'], // Added for Lokus MVP
        ]);

        $validated['password'] = Hash::make($validated['password']);

        event(new Registered(($user = User::create($validated))));

        Auth::login($user);

        $this->redirectIntended(route('dashboard', absolute: false), navigate: true);
    }
}; ?>

<div class="flex flex-col">
    <x-auth-header :title="__('Join Lokus')" :description="__('Create your account to start finding or listing properties')" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center mb-6" :status="session('status')" />

    <form wire:submit="register" class="space-y-6">
        <!-- Name -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="name"
                :label="__('Full Name')"
                type="text"
                required
                autofocus
                autocomplete="name"
                placeholder="Enter your full name"
                class="w-full"
            />
        </div>

        <!-- Email Address -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="email"
                :label="__('Email Address')"
                type="email"
                required
                autocomplete="email"
                placeholder="Enter your email address"
                class="w-full"
            />
        </div>

        <!-- Role Selection -->
        <div class="space-y-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ __('Account Type') }}
            </label>
            <div class="grid grid-cols-1 gap-3">
                <label class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none {{ $role === 'seeker' ? 'border-blue-600 bg-blue-50' : 'border-gray-300 bg-white hover:bg-gray-50' }}">
                    <input type="radio" wire:model.live="role" value="seeker" class="sr-only">
                    <div class="flex items-center">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full border {{ $role === 'seeker' ? 'border-blue-600 bg-blue-600' : 'border-gray-300' }}">
                            @if($role === 'seeker')
                                <div class="h-2 w-2 rounded-full bg-white"></div>
                            @endif
                        </div>
                        <div class="ml-3">
                            <div class="flex items-center">
                                <i class="fas fa-search text-blue-600 mr-2"></i>
                                <span class="block text-sm font-medium text-gray-900">Property Seeker</span>
                            </div>
                            <span class="block text-sm text-gray-500">I'm looking for properties to buy or rent</span>
                        </div>
                    </div>
                </label>

                <label class="relative flex cursor-pointer rounded-lg border p-4 focus:outline-none {{ $role === 'lister' ? 'border-blue-600 bg-blue-50' : 'border-gray-300 bg-white hover:bg-gray-50' }}">
                    <input type="radio" wire:model.live="role" value="lister" class="sr-only">
                    <div class="flex items-center">
                        <div class="flex h-5 w-5 items-center justify-center rounded-full border {{ $role === 'lister' ? 'border-blue-600 bg-blue-600' : 'border-gray-300' }}">
                            @if($role === 'lister')
                                <div class="h-2 w-2 rounded-full bg-white"></div>
                            @endif
                        </div>
                        <div class="ml-3">
                            <div class="flex items-center">
                                <i class="fas fa-home text-blue-600 mr-2"></i>
                                <span class="block text-sm font-medium text-gray-900">Property Lister</span>
                            </div>
                            <span class="block text-sm text-gray-500">I want to list properties for sale or rent</span>
                        </div>
                    </div>
                </label>
            </div>
        </div>

        <!-- Password -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="password"
                :label="__('Password')"
                type="password"
                required
                autocomplete="new-password"
                placeholder="Create a strong password"
                viewable
                class="w-full"
            />
        </div>

        <!-- Confirm Password -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="password_confirmation"
                :label="__('Confirm Password')"
                type="password"
                required
                autocomplete="new-password"
                placeholder="Confirm your password"
                viewable
                class="w-full"
            />
        </div>

        <!-- Terms and Privacy -->
        <div class="text-sm text-gray-600">
            By creating an account, you agree to our
            <a href="#" class="text-blue-600 hover:text-blue-700">Terms of Service</a>
            and
            <a href="#" class="text-blue-600 hover:text-blue-700">Privacy Policy</a>.
        </div>

        <!-- Submit Button -->
        <div class="pt-2">
            <flux:button type="submit" variant="primary" class="w-full h-12 text-base font-medium">
                <i class="fas fa-user-plus mr-2"></i>
                {{ __('Create Account') }}
            </flux:button>
        </div>
    </form>

    <!-- Divider -->
    <div class="relative my-8">
        <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-200"></div>
        </div>
        <div class="relative flex justify-center text-sm">
            <span class="px-4 bg-white text-gray-500">Already have an account?</span>
        </div>
    </div>

    <!-- Login Link -->
    <div class="text-center">
        <a href="{{ route('login') }}" class="inline-flex items-center justify-center w-full h-12 px-4 text-base font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors" wire:navigate>
            <i class="fas fa-sign-in-alt mr-2"></i>
            Sign In Instead
        </a>
    </div>
</div>
