<?php

use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Livewire\Attributes\Layout;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth')] class extends Component {
    public string $name = '';
    public string $email = '';
    public string $password = '';
    public string $password_confirmation = '';
    public string $role = 'seeker'; // Added for Lokus MVP

    /**
     * Handle an incoming registration request.
     */
    public function register(): void
    {
        $validated = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:' . User::class],
            'password' => ['required', 'string', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'string', 'in:seeker,lister'], // Added for Lokus MVP
        ]);

        $validated['password'] = Hash::make($validated['password']);

        event(new Registered(($user = User::create($validated))));

        Auth::login($user);

        $this->redirectIntended(route('dashboard', absolute: false), navigate: true);
    }
}; ?>

<div class="flex flex-col">
    <x-auth-header :title="__('Join Lokus')" :description="__('Create your account to start finding or listing properties')" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center mb-6" :status="session('status')" />

    <form wire:submit="register" class="space-y-6">
        <!-- Name -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="name"
                :label="__('Full Name')"
                type="text"
                required
                autofocus
                autocomplete="name"
                placeholder="Enter your full name"
                class="w-full"
            />
        </div>

        <!-- Email Address -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="email"
                :label="__('Email Address')"
                type="email"
                required
                autocomplete="email"
                placeholder="Enter your email address"
                class="w-full"
            />
        </div>

        <!-- Role Selection -->
        <div class="space-y-2">
            <flux:select wire:model.live="role" id="role" label="{{ __('Account Type') }}">
                <option value="seeker">{{ __('Property Seeker - I\'m looking for properties to buy or rent') }}</option>
                <option value="lister">{{ __('Property Lister - I want to list properties for sale or rent') }}</option>
            </flux:select>

            <!-- Role Description -->
            <div class="mt-3 p-4 rounded-lg bg-blue-50 border border-blue-200">
                <div class="flex items-start">
                    @if($this->role === 'seeker')
                        <i class="fas fa-search text-blue-600 mr-3 mt-1"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-900">Property Seeker Account</h4>
                            <p class="text-sm text-blue-700 mt-1">Perfect for finding your dream home! You'll be able to browse properties, save favorites, and contact property listers directly.</p>
                        </div>
                    @else
                        <i class="fas fa-home text-blue-600 mr-3 mt-1"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-900">Property Lister Account</h4>
                            <p class="text-sm text-blue-700 mt-1">Great for property owners and agents! You'll be able to create property listings, manage your properties, and connect with potential buyers or renters.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Password -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="password"
                :label="__('Password')"
                type="password"
                required
                autocomplete="new-password"
                placeholder="Create a strong password"
                viewable
                class="w-full"
            />
        </div>

        <!-- Confirm Password -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="password_confirmation"
                :label="__('Confirm Password')"
                type="password"
                required
                autocomplete="new-password"
                placeholder="Confirm your password"
                viewable
                class="w-full"
            />
        </div>

        <!-- Terms and Privacy -->
        <div class="text-sm text-gray-600">
            By creating an account, you agree to our
            <a href="#" class="text-blue-600 hover:text-blue-700">Terms of Service</a>
            and
            <a href="#" class="text-blue-600 hover:text-blue-700">Privacy Policy</a>.
        </div>

        <!-- Submit Button -->
        <div class="pt-2">
            <flux:button type="submit" variant="primary" class="w-full h-12 text-base font-medium">
                <i class="fas fa-user-plus mr-2"></i>
                {{ __('Create Account') }}
            </flux:button>
        </div>
    </form>

    <!-- Divider -->
    <div class="relative my-8">
        <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-200"></div>
        </div>
        <div class="relative flex justify-center text-sm">
            <span class="px-4 bg-white text-gray-500">Already have an account?</span>
        </div>
    </div>

    <!-- Login Link -->
    <div class="text-center">
        <a href="{{ route('login') }}" class="inline-flex items-center justify-center w-full h-12 px-4 text-base font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors" wire:navigate>
            <i class="fas fa-sign-in-alt mr-2"></i>
            Sign In Instead
        </a>
    </div>
</div>
